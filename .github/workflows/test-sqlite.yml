name: Run Tests (SQLite)

permissions:
  contents: read

on:
  workflow_call:
  workflow_dispatch:

jobs:
  test-sqlite:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4

      - name: Set up PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: '8.4'
          extensions: pcntl, pdo_sqlite, sqlite3, opcache, intl, zip
          coverage: none

      - name: Install Composer dependencies
        run: composer install --prefer-dist --no-interaction --no-progress

      - name: Set up Bun
        run: |
          curl -fsSL https://bun.sh/install | bash
          export BUN_INSTALL="$HOME/.bun"
          export PATH="$BUN_INSTALL/bin:$PATH"
          bun install
          bun run build

      - name: Prepare <PERSON> environment
        run: |
          cp .env.example .env
          php artisan key:generate
        env:
          DB_CONNECTION: sqlite
          DB_DATABASE: ":memory:"

      - name: Create SQLite database file (for file-based tests)
        run: |
          mkdir -p database
          touch database/database.sqlite

      - name: Run migrations
        run: php artisan migrate --force
        env:
          DB_CONNECTION: sqlite
          DB_DATABASE: database/database.sqlite

      - name: Run tests
        run: vendor/bin/phpunit --dont-report-useless-tests
        env:
          DB_CONNECTION: sqlite
          DB_DATABASE: ":memory:"
