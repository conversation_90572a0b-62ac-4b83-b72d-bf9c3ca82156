name: Run Tests (PostgreSQL)

permissions:
  contents: read

on:
  workflow_call:
  workflow_dispatch:

jobs:
  test:
    runs-on: ubuntu-latest
    services:
      postgres:
        image: postgres:17-alpine
        ports:
          - 5432:5432
        env:
          POSTGRES_DB: tiny_url_test
          POSTGRES_USER: testuser
          POSTGRES_PASSWORD: testpass
        options: >-
          --health-cmd="pg_isready -U testuser -d tiny_url_test" --health-interval=10s --health-timeout=5s --health-retries=5

    steps:
      - uses: actions/checkout@v4

      - name: Set up PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: '8.4'
          extensions: pcntl, pdo_pgsql, pgsql, opcache, intl, zip
          coverage: none

      - name: Install Composer dependencies
        run: composer install --prefer-dist --no-interaction --no-progress

      - name: Set up Bun
        run: |
          curl -fsSL https://bun.sh/install | bash
          export BUN_INSTALL="$HOME/.bun"
          export PATH="$BUN_INSTALL/bin:$PATH"
          bun install
          bun run build

      - name: Prepare <PERSON> environment
        run: |
          cp .env.example .env
          php artisan key:generate
        env:
          DB_CONNECTION: pgsql
          DB_HOST: 127.0.0.1
          DB_PORT: 5432
          DB_DATABASE: tiny_url_test
          DB_USERNAME: testuser
          DB_PASSWORD: testpass

      - name: Run migrations
        run: php artisan migrate --force
        env:
          DB_CONNECTION: pgsql
          DB_HOST: 127.0.0.1
          DB_PORT: 5432
          DB_DATABASE: tiny_url_test
          DB_USERNAME: testuser
          DB_PASSWORD: testpass

      - name: Run tests
        run: vendor/bin/phpunit --dont-report-useless-tests
        env:
          DB_CONNECTION: pgsql
          DB_HOST: 127.0.0.1
          DB_PORT: 5432
          DB_DATABASE: tiny_url_test
          DB_USERNAME: testuser
          DB_PASSWORD: testpass
