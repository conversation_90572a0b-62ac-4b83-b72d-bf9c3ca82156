APP_NAME=Linanok
APP_ENV=production
APP_KEY=base64:aP28hHSMnZ5BDSzG1N2N4A3swRcM7+HYugXSLXoJIHc=
APP_DEBUG=false
APP_TIMEZONE=UTC
APP_URL=http://localhost:8000

OCTANE_WORKERS=8
QUEUE_WORKER_REPLICAS=4

TRUSTED_PROXIES=*

APP_MAINTENANCE_DRIVER=file

BCRYPT_ROUNDS=12

LOG_CHANNEL=stack
LOG_STACK=single
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

# Database Configuration for Docker
# For production with PostgreSQL (recommended)
DB_CONNECTION=pgsql
DB_HOST=postgres
DB_PORT=5432
DB_DATABASE=linanok
DB_USERNAME=postgres
DB_PASSWORD=postgres
# For development with SQLite (uncomment to use)
# DB_CONNECTION=sqlite
# DB_DATABASE=/app/database/database.sqlite

SESSION_DRIVER=redis
SESSION_LIFETIME=120
SESSION_ENCRYPT=false
SESSION_PATH=/
SESSION_DOMAIN=null

FILESYSTEM_DISK=local
QUEUE_CONNECTION=database

CACHE_STORE=redis
CACHE_PREFIX=

REDIS_CLIENT=predis
REDIS_HOST=redis
REDIS_PASSWORD=null
REDIS_PORT=6379
REDIS_DB=0
REDIS_CACHE_DB=1
REDIS_CACHE_CONNECTION=cache

VITE_APP_NAME="${APP_NAME}"
